-- Name: f_dm_foc_ict_item_dtl_inner; Type: Function; Schema: fin_dm_opt_foi;
-- 专门处理ICT产业数据的优化函数
-- 优化策略：
-- 1. 使用临时表替代WITH语句
-- 2. 明确字段列表，避免SELECT *
-- 3. 优化JOIN操作，避免在等式左侧做加工处理
-- 4. 使用临时表减少子查询
-- 5. 考虑分区和索引优化
-- 6. 提前过滤数据减少数据传输

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_ict_item_dtl_inner(f_year character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人: SQL优化专家
背景描述：ICT产业专用的发货明细处理函数
          1. 贴源层的实际发货明细表关联维表带出重量级团队,采购专家团字段
          2. 将单ITEM的品类数据条目打上标识
          3. 专门处理ICT产业数据，应用所有SQL优化策略
参数描述：f_year：年月参数
          x_result_status：是否成功
事例：SELECT FIN_DM_OPT_FOI.f_dm_foc_ict_item_dtl_inner('202412')
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_ICT_ITEM_DTL_INNER'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION_ID BIGINT ; --最新的品类-专家团映射维版本号
  V_DIM_VERSION_ID2 BIGINT; 
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_SQL TEXT;
  V_TO_TABLE VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
  V_LV0_PARA VARCHAR(100) := '104364'; --IRB
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --清空目标表数据:
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE PERIOD_ID = '||F_YEAR||';' ;
  EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'表的'||F_YEAR||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 查询该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
    WHERE VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
        
  -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
    SELECT VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
      WHERE VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
          AND DEL_FLAG = 'N'
          AND STATUS = 1
          AND UPPER(DATA_TYPE) = 'CATEGORY';
  ELSE
    --新版本号赋值
    SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S')
      INTO V_VERSION_ID
      FROM DUAL;
       
    --往版本信息表记录本次TOP品类版本号
    INSERT INTO FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    (VERSION_ID, PARENT_VERSION_ID, VERSION, STATUS, VERSION_TYPE, DATA_TYPE,
     CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG, IS_RUNNING)
     VALUES
     (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
  END IF;
  
  --查询品类专家团映射关系的最新版本号
  SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID2 
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1; 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往ICT版本信息表记录TOP品类版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 优化策略1: 创建品类专家团映射临时表，替代WITH语句
  DROP TABLE IF EXISTS CEG_ICT_TEMP;
  CREATE TEMPORARY TABLE CEG_ICT_TEMP AS (
    SELECT DISTINCT 
           CATEGORY_CODE,
           CATEGORY_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_CN_NAME,
           L3_CEG_SHORT_CN_NAME,
           L4_CEG_CODE,
           L4_CEG_CN_NAME,
           L4_CEG_SHORT_CN_NAME
    FROM (
      SELECT CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,
             ROW_NUMBER() OVER(PARTITION BY CATEGORY_CODE,CATEGORY_CN_NAME ORDER BY 'ICT' ASC) AS RANK
      FROM (
        SELECT VERSION_ID,CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
               L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME
        FROM FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D 
        WHERE VERSION_ID = V_DIM_VERSION_ID2 
          AND DEL_FLAG = 'N'
      ) sub
    ) ranked
    WHERE RANK = 1
  );
  
  -- 为临时表创建索引
  CREATE INDEX idx_ceg_ict_temp_category ON CEG_ICT_TEMP(CATEGORY_CODE);

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建ICT品类专家团映射临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 优化策略2: 创建基础数据临时表，提前过滤和预处理
  DROP TABLE IF EXISTS BASE_DATA_ICT_TEMP;
  CREATE TEMPORARY TABLE BASE_DATA_ICT_TEMP AS (
    SELECT
           D.PERIOD_ID,
           D.ITEM_CODE,
           D.SHIP_QUANTITY,
           D.PARENTPARTNUMBER,
           D.PARENT_SHIP_QUANTITY,
           D.NON_SALE_FLAG,
           D.PROD_KEY,
           D.MAIN_DIMENSION_KEY,
           D.GEO_PC_KEY,
           D.MODEL_NUM,
           D.RECOGNISE_TYPE_ID,
           D.IS_RESALE_FLAG,
           D.PRIMARY_ID
    FROM FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I D
    WHERE D.PERIOD_ID = CAST(F_YEAR AS BIGINT)
      AND D.SHIP_QUANTITY >= 0 -- 数量非负数
      AND D.MODEL_NUM IN ('P', 'SI') --取P和SI件
      AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
      AND D.IS_RESALE_FLAG = 'N' --去除转售重复数据
      AND CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP)-3
  );

  -- 为基础数据临时表创建索引
  CREATE INDEX idx_base_data_ict_item ON BASE_DATA_ICT_TEMP(ITEM_CODE);
  CREATE INDEX idx_base_data_ict_prod ON BASE_DATA_ICT_TEMP(PROD_KEY);
  CREATE INDEX idx_base_data_ict_geo ON BASE_DATA_ICT_TEMP(GEO_PC_KEY);
  CREATE INDEX idx_base_data_ict_dim ON BASE_DATA_ICT_TEMP(MAIN_DIMENSION_KEY);
  CREATE INDEX idx_base_data_ict_primary ON BASE_DATA_ICT_TEMP(PRIMARY_ID);

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建ICT基础数据临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 优化策略3: 创建产品维度临时表，预先关联重量级团队信息
  DROP TABLE IF EXISTS PRODUCT_DIM_ICT_TEMP;
  CREATE TEMPORARY TABLE PRODUCT_DIM_ICT_TEMP AS (
    SELECT
           CP.PROD_KEY,
           CP.LV0_PROD_RND_TEAM_CODE,
           CP.LV0_PROD_RD_TEAM_CN_NAME,
           CP.LV1_PROD_RND_TEAM_CODE,
           CP.LV1_PROD_RD_TEAM_CN_NAME,
           CP.LV2_PROD_RND_TEAM_CODE,
           CP.LV2_PROD_RD_TEAM_CN_NAME,
           CP.LV3_PROD_RND_TEAM_CODE,
           CP.LV3_PROD_RD_TEAM_CN_NAME,
           CASE WHEN CP.LV0_PROD_LIST_CODE IN ('PDCG901159', 'PDCG817296', 'PDCG901160') THEN
                     CP.LV0_PROD_LIST_CODE ELSE '其他'
                END AS LV0_PROD_LIST_CODE,
           CASE WHEN CP.LV0_PROD_LIST_CODE IN ('PDCG901159', 'PDCG817296', 'PDCG901160') THEN
                     CP.LV0_PROD_LIST_CN_NAME ELSE '其他'
                END AS LV0_PROD_LIST_CN_NAME,
           CASE WHEN CP.LV0_PROD_LIST_CODE IN ('PDCG901159', 'PDCG817296', 'PDCG901160') THEN
                     CP.LV0_PROD_LIST_EN_NAME ELSE 'OTHER'
                END AS LV0_PROD_LIST_EN_NAME,
           CP.PROD_CODE,
           CP.CN_DESCRIPTION
    FROM DMDIM.DM_DIM_PRODUCT_D CP
    WHERE CP.LV0_PROD_RND_TEAM_CODE = V_LV0_PARA  --ICT限制为IRB
  );

  -- 为产品维度临时表创建索引
  CREATE INDEX idx_prod_dim_ict_key ON PRODUCT_DIM_ICT_TEMP(PROD_KEY);
  CREATE INDEX idx_prod_dim_ict_code ON PRODUCT_DIM_ICT_TEMP(PROD_CODE);

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建ICT产品维度临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 优化策略4: 创建物料维度临时表
  DROP TABLE IF EXISTS MATERIAL_DIM_ICT_TEMP;
  CREATE TEMPORARY TABLE MATERIAL_DIM_ICT_TEMP AS (
    SELECT DISTINCT
           M.ITEM_CODE,
           M.ITEM_NAME,
           M.ITEM_SUBTYPE_CODE,
           M.ITEM_SUBTYPE_CN_NAME
    FROM DWRDIM.DWR_DIM_MATERIAL_CODE_D M
    WHERE EXISTS (SELECT 1 FROM BASE_DATA_ICT_TEMP B WHERE B.ITEM_CODE = M.ITEM_CODE)
  );

  -- 为物料维度临时表创建索引
  CREATE INDEX idx_material_dim_ict_item ON MATERIAL_DIM_ICT_TEMP(ITEM_CODE);
  CREATE INDEX idx_material_dim_ict_subtype ON MATERIAL_DIM_ICT_TEMP(ITEM_SUBTYPE_CODE);

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建ICT物料维度临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 优化策略5: 创建最终结果临时表，避免复杂的子查询
  DROP TABLE IF EXISTS FINAL_RESULT_ICT_TEMP;
  CREATE TEMPORARY TABLE FINAL_RESULT_ICT_TEMP AS (
    SELECT
           V_VERSION_ID AS VERSION_ID,
           CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
           D.PERIOD_ID,
           CP.LV0_PROD_RND_TEAM_CODE,
           CP.LV0_PROD_RD_TEAM_CN_NAME,
           CP.LV1_PROD_RND_TEAM_CODE,
           CP.LV1_PROD_RD_TEAM_CN_NAME,
           CP.LV2_PROD_RND_TEAM_CODE,
           CP.LV2_PROD_RD_TEAM_CN_NAME,
           CP.LV3_PROD_RND_TEAM_CODE,
           CP.LV3_PROD_RD_TEAM_CN_NAME,
           CP.LV0_PROD_LIST_CODE,
           CP.LV0_PROD_LIST_CN_NAME,
           CP.LV0_PROD_LIST_EN_NAME,
           PD.PRODUCT_DIMENSION_CODE AS DIMENSION_CODE,
           PD.PRODUCT_DIMENSION_CN_NAME AS DIMENSION_CN_NAME,
           PD.PRODUCT_DIMENSION_EN_NAME AS DIMENSION_EN_NAME,
           PD.DIMENSION_SUBCATEGORY_CODE,
           PD.DIMENSION_SUBCATEGORY_CN_NAME,
           PD.DIMENSION_SUBCATEGORY_EN_NAME,
           PD.DIMENSION_SUB_DETAIL_CODE,
           PD.DIMENSION_SUB_DETAIL_CN_NAME,
           PD.DIMENSION_SUB_DETAIL_EN_NAME,
           ZJ.L3_CEG_CODE,
           ZJ.L3_CEG_CN_NAME,
           ZJ.L3_CEG_SHORT_CN_NAME,
           ZJ.L4_CEG_CODE,
           ZJ.L4_CEG_CN_NAME,
           ZJ.L4_CEG_SHORT_CN_NAME,
           M.ITEM_SUBTYPE_CODE AS CATEGORY_CODE,
           M.ITEM_SUBTYPE_CN_NAME AS CATEGORY_CN_NAME,
           M.ITEM_CODE,
           M.ITEM_NAME AS ITEM_CN_NAME,
           D.SHIP_QUANTITY,
           COALESCE(T.RMB_COST_AMT, 0) AS RMB_COST_AMT,
           CP.PROD_CODE,
           CP.CN_DESCRIPTION,
           D.PARENTPARTNUMBER,
           D.PARENT_SHIP_QUANTITY,
           D.NON_SALE_FLAG,
           DECODE(RC.OVERSEA_FLAG,'Y','O','N','I') AS OVERSEA_FLAG,
           D.PRIMARY_ID,
           COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME, '其他') AS L1_NAME,
           L2C.L2_NAME AS L2_NAME,
           COUNT(L2C.L2_NAME) OVER(PARTITION BY COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME)) AS FLAG
    FROM BASE_DATA_ICT_TEMP D
    INNER JOIN MATERIAL_DIM_ICT_TEMP M ON D.ITEM_CODE = M.ITEM_CODE
    INNER JOIN PRODUCT_DIM_ICT_TEMP CP ON D.PROD_KEY = CP.PROD_KEY
    INNER JOIN CEG_ICT_TEMP ZJ ON M.ITEM_SUBTYPE_CODE = ZJ.CATEGORY_CODE
    LEFT JOIN DM_FOC_DIM_PRODUCTDIMENSION_D PD ON D.MAIN_DIMENSION_KEY = PD.DIMENSION_KEY
    LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T T ON D.PRIMARY_ID = T.PRIMARY_ID
    LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1
      ON CP.LV1_PROD_RND_TEAM_CODE = L1.LV1_CODE
     AND CP.LV2_PROD_RND_TEAM_CODE = L1.LV2_CODE
     AND CP.LV3_PROD_RND_TEAM_CODE = L1.LV3_CODE
     AND L1.LV3_CODE IS NOT NULL
     AND L1.DEL_FLAG = 'N' AND UPPER(L1.STATUS) = 'SUBMIT'
    LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1B
      ON CP.LV1_PROD_RND_TEAM_CODE = L1B.LV1_CODE
     AND CP.LV2_PROD_RND_TEAM_CODE = L1B.LV2_CODE
     AND L1B.LV2_CODE IS NOT NULL
     AND L1B.LV3_CODE IS NULL
     AND L1B.DEL_FLAG = 'N' AND UPPER(L1B.STATUS) = 'SUBMIT'
    LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG
      ON CP.LV1_PROD_RD_TEAM_CN_NAME = CFG.LV1_PROD_RD_TEAM_CN_NAME
     AND CP.LV2_PROD_RD_TEAM_CN_NAME = CFG.LV2_PROD_RD_TEAM_CN_NAME
     AND CP.LV3_PROD_RD_TEAM_CN_NAME = CFG.LV3_PROD_RD_TEAM_CN_NAME
     AND CFG.LV3_PROD_RD_TEAM_CN_NAME IS NOT NULL
    LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG2
      ON CP.LV1_PROD_RD_TEAM_CN_NAME = CFG2.LV1_PROD_RD_TEAM_CN_NAME
     AND CP.LV2_PROD_RD_TEAM_CN_NAME = CFG2.LV2_PROD_RD_TEAM_CN_NAME
     AND CFG2.LV2_PROD_RD_TEAM_CN_NAME IS NOT NULL
     AND CFG2.LV3_PROD_RD_TEAM_CN_NAME IS NULL
    LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_COA_L1_T L2C
      ON COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME) = L2C.L1_NAME
     AND CP.PROD_CODE = L2C.COA_CODE
     AND L2C.DEL_FLAG = 'N' AND UPPER(L2C.STATUS) = 'SUBMIT'
    LEFT JOIN DWRDIM.DWR_DIM_REGION_RC_D RC ON D.GEO_PC_KEY = RC.GEO_PC_KEY
  );

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建ICT最终结果临时表完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/
